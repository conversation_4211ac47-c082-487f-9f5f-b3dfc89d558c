/**
 * course-edit.wxss - 课程编辑页面样式文件（高端美化版）
 *
 * 设计理念：
 * 1. 现代化设计：采用最新的设计趋势和视觉语言
 * 2. 高端质感：通过精致的阴影、渐变和动画提升品质感
 * 3. 用户体验：优化交互流程，提供直观的视觉反馈
 * 4. 信息架构：清晰的信息层次和逻辑分组
 * 5. 品牌一致性：与整个应用的高端定位保持一致
 *
 * 视觉特点：
 * - 深度设计：多层次阴影营造空间感
 * - 渐变效果：微妙的渐变增加视觉丰富度
 * - 圆角设计：现代化的圆角处理
 * - 色彩系统：精心调配的色彩搭配
 * - 动画交互：流畅的过渡动画
 *
 * 技术特点：
 * - CSS3特性：充分利用现代CSS特性
 * - 响应式设计：完美适配各种屏幕尺寸
 * - 性能优化：高效的样式组织和渲染
 */

/*
 * 页面根元素样式
 * 设置页面的基础高度，为固定布局提供基础
 */
page {
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/**
 * 表单区域样式 - 高端美化版
 *
 * 设计理念：
 * 采用现代化的容器设计，通过精致的背景和布局营造高端感
 * 使用渐变背景和微妙的纹理效果提升视觉品质
 *
 * 视觉特点：
 * - 渐变背景：营造深度和层次感
 * - 适度内边距：为内容提供舒适的呼吸空间
 * - 最小高度：确保页面填满屏幕
 * - 盒模型：使用border-box确保尺寸计算准确
 */
.form-area {
  /*
   * 内边距设计 - 紧凑化
   * 16rpx: 减少内边距，提高空间利用率
   */
  padding: 16rpx;

  /*
   * 背景设计 - 透明背景让页面渐变显示
   * 移除原有的单色背景，让页面级渐变背景显示
   */
  background: transparent;

  /*
   * 高度设置
   * 确保表单区域至少填满整个视口
   */
  min-height: 100vh;

  /*
   * 盒模型
   * 使用border-box确保内边距不会影响总宽度
   */
  box-sizing: border-box;
}

/**
 * 表单分区样式 - 高端美化版
 *
 * 设计理念：
 * 每个表单分区都是一个独立的卡片容器，通过精致的设计营造高端感
 * 使用现代化的卡片设计语言，包括阴影、圆角、渐变等元素
 *
 * 视觉特点：
 * - 卡片设计：每个分区都是独立的卡片
 * - 多层阴影：营造浮起的视觉效果
 * - 渐变背景：微妙的渐变增加质感
 * - 圆角设计：现代化的圆角处理
 * - 适度间距：分区之间保持舒适的间距
 */
.form-section {
  /*
   * 背景设计 - 微妙的渐变背景
   * 从纯白到极浅的灰色，营造微妙的质感
   */
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);

  /*
   * 圆角设计
   * 16rpx: 较大的圆角，营造现代化的视觉效果
   */
  border-radius: 16rpx;

  /*
   * 多层阴影设计
   * 第一层：主要阴影，营造浮起效果
   * 第二层：边缘阴影，增加深度感
   * 第三层：内阴影，增加质感
   */
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);

  /*
   * 内边距设计 - 紧凑化
   * 20rpx: 减少内边距，提高空间利用率
   */
  padding: 20rpx;

  /*
   * 间距设计 - 紧凑化
   * 20rpx: 减少分区间距，让页面更紧凑
   */
  margin-bottom: 20rpx;

  /*
   * 边框设计
   * 极淡的边框增加精致感
   */
  border: 1rpx solid rgba(0, 0, 0, 0.02);

  /*
   * 过渡动画
   * 为悬停效果提供平滑的过渡
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/**
 * 表单分区悬停效果
 * 增加交互反馈，提升用户体验
 */
.form-section:hover {
  /*
   * 悬停时的阴影增强
   * 更明显的阴影营造更强的浮起感
   */
  box-shadow:
    0 12rpx 48rpx rgba(0, 0, 0, 0.12),
    0 4rpx 16rpx rgba(0, 0, 0, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);

  /*
   * 轻微的上移效果
   * 增强悬停的视觉反馈
   */
  transform: translateY(-2rpx);
}

/**
 * 表单项容器样式 - 紧凑优化版
 *
 * 设计理念：
 * 减少不必要的留白，让输入框更突出，提高空间利用率
 * 通过更明显的边框和背景让用户清楚知道输入区域
 *
 * 优化特点：
 * - 紧凑布局：减少内边距和间距
 * - 突出输入：更明显的边框和背景
 * - 高效利用：减少垂直空间浪费
 */
.form-item {
  /*
   * 间距设计 - 紧凑化
   * 12rpx: 减少表单项之间的间距，提高空间利用率
   */
  margin-bottom: 12rpx;

  /*
   * 背景设计 - 更明显
   * 白色背景让输入区域更突出
   */
  background-color: #ffffff;

  /*
   * 圆角设计
   * 8rpx: 更小的圆角，更紧凑
   */
  border-radius: 8rpx;

  /*
   * 内边距设计 - 紧凑化
   * 16rpx: 减少内边距，让内容更紧凑
   */
  padding: 16rpx;

  /*
   * 边框设计 - 更明显
   * 更明显的边框让用户清楚看到输入区域
   */
  border: 1rpx solid rgba(0, 0, 0, 0.12);

  /*
   * 过渡动画
   * 为交互状态提供平滑过渡
   */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/**
 * 表单项聚焦状态 - 强化版
 * 当表单项内的输入框获得焦点时的样式，让用户清楚知道当前输入位置
 */
.form-item:focus-within {
  /*
   * 聚焦时的背景色
   * 保持白色背景
   */
  background-color: #ffffff;

  /*
   * 聚焦时的边框 - 更明显
   * 更明显的蓝色边框表示激活状态
   */
  border-color: #1890ff;

  /*
   * 聚焦时的阴影 - 更突出
   * 明显的蓝色阴影让用户清楚看到聚焦区域
   */
  box-shadow: 0 0 0 3rpx rgba(24, 144, 255, 0.2);
}

/**
 * 表单项悬停状态 - 强化版
 * 增加明显的交互反馈
 */
.form-item:hover {
  /*
   * 悬停时的背景色
   * 保持白色背景
   */
  background-color: #ffffff;

  /*
   * 悬停时的边框 - 更明显
   * 更深的边框色让用户知道可以点击
   */
  border-color: rgba(0, 0, 0, 0.2);

  /*
   * 悬停时的阴影
   * 轻微的阴影增加立体感
   */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/**
 * 表单标签样式 - 高端美化版
 *
 * 设计理念：
 * 标签采用现代化的排版设计，通过精致的字体和颜色营造专业感
 * 使用渐变色和微妙的阴影增加视觉层次
 *
 * 视觉特点：
 * - 渐变文字：微妙的渐变色增加质感
 * - 优化字重：更精确的字重控制
 * - 文字阴影：极淡的阴影增加立体感
 * - 间距优化：更合理的间距设计
 */
.label {
  /*
   * 显示方式
   * 块级元素，独占一行
   */
  display: block;

  /*
   * 间距设计 - 紧凑化
   * 8rpx: 减少与输入框的间距，让布局更紧凑
   */
  margin-bottom: 8rpx;

  /*
   * 字体设计 - 统一rpx单位
   * 28rpx: 参考profile页面中字体标准（正文内容）
   */
  font-size: 28rpx;

  /*
   * 字重设计
   * 600: 半粗体，既突出又不过于厚重
   */
  font-weight: 600;

  /*
   * 颜色设计 - 渐变文字色
   * 从深灰到稍浅的灰色，营造微妙的层次感
   */
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  /*
   * 行高优化
   * 1.4: 更舒适的行高
   */
  line-height: 1.4;

  /*
   * 字母间距
   * 微妙的字母间距增加精致感
   */
  letter-spacing: 0.5rpx;
}

/**
 * 必填字段标识样式 - 高端美化版
 *
 * 设计理念：
 * 必填标识采用更精致的设计，通过渐变色和动画效果增加视觉吸引力
 * 使用现代化的视觉语言，既突出重要性又保持美观
 *
 * 视觉特点：
 * - 渐变星号：红色渐变增加质感
 * - 微动画：轻微的脉冲动画吸引注意
 * - 阴影效果：增加立体感
 * - 精确定位：更精确的位置控制
 */
.label.required::after {
  /*
   * 内容设置
   * 空格+星号，保持与文字的适当间距
   */
  content: ' *';

  /*
   * 颜色设计 - 渐变红色
   * 从鲜艳的红色到稍深的红色
   */
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  /*
   * 字体设计
   * 700: 粗体，突出显示
   */
  font-weight: 700;

  /*
   * 文字阴影
   * 极淡的红色阴影增加立体感
   */
  text-shadow: 0 1rpx 2rpx rgba(238, 90, 82, 0.2);

  /*
   * 动画效果
   * 轻微的脉冲动画吸引注意力
   */
  animation: requiredPulse 2s ease-in-out infinite;
}

/**
 * 必填字段脉冲动画
 * 轻微的透明度变化，不会过于干扰
 */
@keyframes requiredPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/**
 * 输入提示文字样式 - 统一rpx单位版本
 *
 * 设计理念：
 * 提示文字采用精致的排版和颜色设计，提供友好的用户指导
 * 参考profile页面规范使用24rpx小字体
 */
.input-tip {
  /*
   * 显示方式
   * 块级元素，独占一行
   */
  display: block;

  /*
   * 间距设计
   * 8rpx: 与输入框保持适度间距
   */
  margin-top: 8rpx;

  /*
   * 字体设计 - 统一rpx单位
   * 24rpx: 参考profile页面小字体标准（辅助信息）
   */
  font-size: 24rpx;

  /*
   * 颜色设计
   * 温和的灰色，提供信息但不突出
   */
  color: #8492a6;

  /*
   * 行高优化
   * 1.4: 提升小字号的可读性
   */
  line-height: 1.4;

  /*
   * 字体样式
   * 斜体增加提示的视觉区分
   */
  font-style: italic;
}

/**
 * 错误提示文字样式 - 高端美化版
 *
 * 设计理念：
 * 错误提示采用现代化的警告设计，通过渐变色和图标增强视觉效果
 * 确保错误信息既突出又美观
 *
 * 视觉特点：
 * - 渐变红色：增加视觉冲击力
 * - 图标前缀：增加识别度
 * - 动画效果：吸引用户注意
 * - 圆角背景：现代化的容器设计
 */
.error-text {
  /*
   * 显示方式
   * 块级元素，独占一行
   */
  display: block;

  /*
   * 间距设计
   * 8rpx: 与输入框保持适度间距
   */
  margin-top: 8rpx;

  /*
   * 字体设计 - 统一rpx单位
   * 24rpx: 参考profile页面小字体标准（辅助信息）
   */
  font-size: 24rpx;

  /*
   * 颜色设计 - 渐变红色
   * 从鲜艳的红色到稍深的红色
   */
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  /*
   * 行高优化
   * 1.4: 提升可读性
   */
  line-height: 1.4;

  /*
   * 字体粗细
   * 500: 中等粗细，确保可读性
   */
  font-weight: 500;

  /*
   * 内边距和背景
   * 轻微的背景色和内边距，形成容器感
   */
  padding: 6rpx 12rpx;
  background-color: rgba(255, 107, 107, 0.05);
  border-radius: 6rpx;
  border-left: 3rpx solid #ff6b6b;

  /*
   * 动画效果
   * 淡入动画，柔和地显示错误信息
   */
  animation: errorFadeIn 0.3s ease-out;
}

/**
 * 错误提示淡入动画
 * 柔和的出现效果，避免突兀
 */
@keyframes errorFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/**
 * 错误提示前缀图标
 * 使用伪元素添加警告图标
 */
.error-text::before {
  content: '⚠ ';
  margin-right: 4rpx;
  color: #ff6b6b;
}

/**
 * 讲师选择器样式 - 高端美化版
 *
 * 设计理念：
 * 讲师选择器采用现代化的卡片设计，通过头像和信息展示提升用户体验
 * 使用精致的布局和交互效果营造专业感
 */
.coach-selector {
  /*
   * 布局设计
   * Flexbox布局，水平排列
   */
  display: flex;
  align-items: center;
  justify-content: space-between;

  /*
   * 尺寸设计 - 加高版本
   * 与单行输入框保持一致的高度
   */
  min-height: 80rpx;

  /*
   * 内边距设计 - 与输入框一致
   * 左右内边距，上下通过负边距处理
   */
  padding: 0 12rpx;

  /*
   * 背景设计 - 更明显
   * 浅灰背景让选择区域更明显
   */
  background-color: #f8f9fa;

  /*
   * 边框设计 - 更明显
   * 更明显的边框让用户知道这是可点击区域
   */
  border: 1rpx solid #e1e5e9;
  border-radius: 6rpx;

  /*
   * 负边距处理
   * 抵消高度增加对容器的影响
   */
  margin: -8rpx 0;

  /*
   * 垂直居中
   * 确保内容在选择器中垂直居中
   */
  display: flex;
  align-items: center;

  /*
   * 过渡动画
   * 为交互状态提供平滑过渡
   */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  /*
   * 鼠标样式
   * 指针样式表示可点击
   */
  cursor: pointer;
}

/**
 * 讲师选择器悬停效果 - 强化版
 */
.coach-selector:hover {
  /*
   * 悬停时的背景变化
   * 更明显的白色背景
   */
  background-color: #ffffff;

  /*
   * 悬停时的边框变化 - 更明显
   * 蓝色边框表示交互状态
   */
  border-color: #1890ff;

  /*
   * 悬停时的阴影 - 更明显
   * 明显的阴影让用户知道可以点击
   */
  box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.2);
}

/**
 * 讲师信息容器
 * 包含头像和详细信息
 */
.coach-info {
  /*
   * 布局设计
   * Flexbox布局，水平排列
   */
  display: flex;
  align-items: center;
  flex: 1;
  gap: 16rpx;
}

/**
 * 讲师头像容器
 * 为头像提供圆形容器和边框
 */
.coach-avatar-container {
  /*
   * 尺寸设计
   * 固定尺寸的圆形容器
   */
  width: 80rpx;
  height: 80rpx;

  /*
   * 圆形设计
   * 50%圆角形成完美圆形
   */
  border-radius: 50%;

  /*
   * 边框设计
   * 微妙的边框增加精致感
   */
  border: 2rpx solid rgba(0, 0, 0, 0.06);

  /*
   * 溢出隐藏
   * 确保头像不会超出圆形边界
   */
  overflow: hidden;

  /*
   * 背景设计
   * 浅灰色背景作为头像加载时的占位
   */
  background-color: #f5f5f5;

  /*
   * 布局设计
   * 居中对齐头像
   */
  display: flex;
  align-items: center;
  justify-content: center;
}

/**
 * 讲师头像图片
 */
.coach-avatar {
  /*
   * 尺寸设计
   * 填满容器
   */
  width: 100%;
  height: 100%;

  /*
   * 对象适配
   * 保持宽高比，裁剪多余部分
   */
  object-fit: cover;
}

/**
 * 默认头像图标
 * 当讲师没有头像时显示的图标
 */
.default-avatar-icon {
  /*
   * 尺寸设计
   * 填满容器
   */
  width: 100%;
  height: 100%;

  /*
   * 布局设计
   * 居中对齐图标
   */
  display: flex;
  align-items: center;
  justify-content: center;

  /*
   * 背景设计
   * 浅灰色背景
   */
  background-color: #f5f5f5;
}

/**
 * 讲师详细信息容器
 */
.coach-details {
  /*
   * 布局设计
   * 垂直排列
   */
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  flex: 1;
}

/**
 * 讲师姓名样式 - 统一rpx单位版本
 */
.coach-name {
  /*
   * 字体设计 - 统一rpx单位
   * 28rpx: 参考profile页面中字体标准（正文内容）
   */
  font-size: 28rpx;
  font-weight: 600;

  /*
   * 颜色设计
   * 深色文字确保可读性
   */
  color: #333333;

  /*
   * 行高设计
   * 优化可读性和垂直居中
   */
  line-height: 1.2;
}

/**
 * 讲师职位样式 - 统一rpx单位版本
 */
.coach-title {
  /*
   * 字体设计 - 统一rpx单位
   * 24rpx: 参考profile页面小字体标准（辅助信息）
   */
  font-size: 24rpx;
  font-weight: 400;

  /*
   * 颜色设计
   * 浅色文字降低视觉权重
   */
  color: #666666;

  /*
   * 行高设计
   * 优化可读性和垂直居中
   */
  line-height: 1.2;
}

/**
 * 讲师选择占位符容器
 */
.coach-placeholder {
  /*
   * 布局设计
   * Flexbox布局，水平排列
   */
  display: flex;
  align-items: center;
  flex: 1;
  gap: 12rpx;
}

/**
 * 占位符图标容器
 */
.placeholder-icon {
  /*
   * 尺寸设计
   * 与头像容器相同的尺寸
   */
  width: 80rpx;
  height: 80rpx;

  /*
   * 圆形设计
   */
  border-radius: 50%;

  /*
   * 背景设计
   * 虚线边框表示占位状态
   */
  border: 2rpx dashed #cccccc;
  background-color: #fafafa;

  /*
   * 布局设计
   * 居中对齐图标
   */
  display: flex;
  align-items: center;
  justify-content: center;
}

/**
 * 占位符文字样式 - 加高版本
 */
.placeholder-text {
  /*
   * 字体设计 - 统一rpx单位
   * 28rpx: 参考profile页面中字体标准（正文内容）
   */
  font-size: 28rpx;

  /*
   * 颜色设计
   * 浅色文字表示占位状态
   */
  color: #999999;

  /*
   * 垂直居中
   * 确保文字在选择器中垂直居中
   */
  line-height: 1.2;
}

/**
 * 讲师选择器箭头
 */
.coach-arrow {
  /*
   * 布局设计
   * 居中对齐箭头图标
   */
  display: flex;
  align-items: center;
  justify-content: center;

  /*
   * 尺寸设计
   * 固定尺寸的点击区域
   */
  width: 32rpx;
  height: 32rpx;
}

/**
 * 统一输入框样式 - 加高版本
 * 包括t-input、t-textarea等组件的文本样式统一，让输入区域更明显
 */
.form-item t-input {
  /*
   * 背景设计 - 让输入框更明显
   * 浅灰色背景让用户清楚看到输入区域
   */
  background-color: #f8f9fa !important;

  /*
   * 边框设计 - 更明显的边框
   * 让用户清楚知道这是输入区域
   */
  border: 1rpx solid #e1e5e9 !important;
  border-radius: 6rpx !important;

  /*
   * 高度设计 - 加大单行输入框高度
   * 通过负边距不影响容器高度
   */
  height: 80rpx !important;
  margin: -8rpx 0 !important; /* 负边距抵消高度增加的影响 */

  /*
   * 垂直居中
   * 确保文字在输入框中垂直居中
   */
  display: flex !important;
  align-items: center !important;

  /*
   * 盒模型
   */
  box-sizing: border-box !important;
}

/**
 * 多行文本框保持原有样式
 */
.form-item t-textarea {
  /*
   * 文本对齐
   * 右对齐显示
   */
  text-align: right !important;

  /*
   * 用户输入文本颜色
   * 黑色文本
   */
  color: #000000 !important;

  /*
   * 字体大小统一 - 统一rpx单位
   * 28rpx: 参考profile页面中字体标准（正文内容）
   */
  font-size: 28rpx !important;
}

/**
 * 输入框占位符文本样式
 * 提示文本用灰色，右对齐
 */
.form-item t-input::placeholder,
.form-item t-textarea::placeholder {
  color: #999999 !important;
  text-align: right !important;
}

/**
 * t-cell组件文本样式统一
 * 用于时间选择器等
 */
.form-item t-cell {
  text-align: right !important;
}

/**
 * 表单项中的t-cell样式 - 加高版本
 * 时间选择器等组件的容器样式
 */
.form-item t-cell {
  /*
   * 高度设计 - 加大Cell高度
   * 让时间选择等操作区域更容易点击
   */
  min-height: 80rpx !important;

  /*
   * 背景和边框
   * 与输入框保持一致的视觉效果
   */
  background-color: #f8f9fa !important;
  border: 1rpx solid #e1e5e9 !important;
  border-radius: 6rpx !important;
  margin: -8rpx 0 !important; /* 负边距抵消高度增加的影响 */

  /*
   * 内边距
   */
  padding: 0 12rpx !important;

  /*
   * 垂直居中
   * 确保内容在Cell中垂直居中
   */
  display: flex !important;
  align-items: center !important;

  /*
   * 盒模型
   */
  box-sizing: border-box !important;
}

/**
 * t-cell的note文本样式 - 加高版本
 * 时间选择器显示的时间文本
 */
.form-item t-cell .t-cell__note {
  color: #000000 !important;
  font-size: 28rpx !important; /* 28rpx: 参考profile页面中字体标准 */
  text-align: right !important;

  /*
   * 垂直居中
   * 确保文字垂直居中
   */
  line-height: 1.2 !important;
}

/**
 * t-cell占位符样式
 * 当没有选择时间时的提示文本
 */
.form-item t-cell[note="请选择时间"] .t-cell__note,
.form-item t-cell[note=""] .t-cell__note {
  color: #999999 !important;
}

/**
 * TDesign输入框内部样式覆盖 - 加高版本
 * 确保输入框内的文本右对齐，加大高度，文字上下居中
 */
.t-input__control {
  text-align: right !important;
  color: #000000 !important;
  font-size: 28rpx !important; /* 28rpx: 参考profile页面中字体标准 */
  background-color: #f8f9fa !important; /* 浅灰背景让输入区域更明显 */
  border: 1rpx solid #e1e5e9 !important; /* 明显的边框 */
  border-radius: 6rpx !important;

  /*
   * 高度和垂直居中设计
   * 增加高度但通过负边距不影响容器高度
   */
  height: 80rpx !important; /* 加大输入框高度 */
  line-height: 80rpx !important; /* 行高与高度一致，实现垂直居中 */
  padding: 0 12rpx !important; /* 只保留左右内边距 */
  margin: -8rpx 0 !important; /* 负边距抵消高度增加对容器的影响 */

  /*
   * 盒模型
   * 确保尺寸计算准确
   */
  box-sizing: border-box !important;

  /*
   * 垂直对齐
   * 确保在容器中垂直居中
   */
  display: flex !important;
  align-items: center !important;
}

.t-input__control::placeholder {
  color: #999999 !important;
  text-align: right !important;
}

/**
 * TDesign文本域样式覆盖 - 突出显示版本
 */
.t-textarea__control {
  text-align: right !important;
  color: #000000 !important;
  font-size: 28rpx !important; /* 28rpx: 参考profile页面中字体标准 */
  background-color: #f8f9fa !important; /* 浅灰背景让输入区域更明显 */
  border: 1rpx solid #e1e5e9 !important; /* 明显的边框 */
  border-radius: 6rpx !important;
  padding: 12rpx !important; /* 增加内边距，扩大可点击区域 */
}

.t-textarea__control::placeholder {
  color: #999999 !important;
  text-align: right !important;
}

/**
 * TDesign Cell组件样式覆盖 - 加高版本
 * 确保时间选择器等cell组件的文本右对齐，并加大高度
 */
.t-cell {
  /*
   * 高度设计 - 加大Cell高度
   * 让时间选择等操作区域更容易点击
   */
  min-height: 80rpx !important;

  /*
   * 垂直居中
   * 确保内容在Cell中垂直居中
   */
  display: flex !important;
  align-items: center !important;

  /*
   * 背景和边框
   * 与输入框保持一致的视觉效果
   */
  background-color: #f8f9fa !important;
  border: 1rpx solid #e1e5e9 !important;
  border-radius: 6rpx !important;
  margin: -8rpx 0 !important; /* 负边距抵消高度增加的影响 */

  /*
   * 内边距
   */
  padding: 0 12rpx !important;

  /*
   * 盒模型
   */
  box-sizing: border-box !important;
}

.t-cell__note {
  color: #000000 !important;
  font-size: 28rpx !important; /* 28rpx: 参考profile页面中字体标准 */
  text-align: right !important;

  /*
   * 垂直居中
   * 确保文字在note区域垂直居中
   */
  line-height: 1.2 !important;
}

/**
 * 空状态的cell note样式
 */
.t-cell__note:empty::before {
  content: "请选择";
  color: #999999 !important;
}

/**
 * 多行文本框样式 - 突出显示版本
 *
 * 设计说明：
 * 为活动详情等多行文本输入提供更明显的样式
 * 让用户清楚知道这是输入区域
 *
 * 优化特点：
 * - 明显背景：更明显的背景色
 * - 清晰边框：更明显的边框让用户知道输入区域
 * - 紧凑高度：减少最小高度，节省空间
 * - 适度内边距：提供舒适的输入体验
 */
.form-item t-textarea {
  background-color: #f8f9fa !important;  /* 背景色：浅灰色，让输入区域更明显 */
  border-radius: 6rpx !important;        /* 圆角：6rpx，与其他输入框一致 */
  border: 1rpx solid #e1e5e9 !important; /* 边框：更明显的边框 */
  min-height: 100rpx !important;         /* 最小高度：减少高度，更紧凑 */
  padding: 12rpx !important;             /* 内边距：增加可点击区域 */
}

/**
 * 标签描述文字样式 - 统一rpx单位版本
 *
 * 设计说明：
 * 为表单标签提供补充说明文字的样式
 * 参考profile页面规范使用24rpx小字体
 */
.label-desc {
  display: block;
  margin-top: 4rpx;
  margin-bottom: 16rpx;
  font-size: 24rpx;                  /* 24rpx: 参考profile页面小字体标准（辅助信息） */
  color: #999;                       /* 字体颜色：浅灰色 */
  line-height: 1.4;
}

/**
 * 图片网格容器样式
 *
 * 设计说明：
 * 使用CSS Grid布局创建响应式的图片网格
 * 支持多张图片的整齐排列和自适应布局
 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);  /* 3列网格布局 */
  gap: 16rpx;                             /* 网格间距：16rpx */
  margin-top: 16rpx;
}

/**
 * 图片项容器样式
 *
 * 设计说明：
 * 每个图片项的容器，包含图片和删除按钮
 * 使用相对定位为删除按钮提供定位基准
 */
.image-item {
  position: relative;
  width: 100%;
  aspect-ratio: 1;                        /* 保持1:1的宽高比 */
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #f5f5f5;
}

/**
 * 课程图片样式
 *
 * 设计说明：
 * 图片填满容器，保持宽高比，支持点击预览
 */
.course-image {
  width: 100%;
  height: 100%;
  object-fit: cover;                      /* 保持宽高比，裁剪多余部分 */
}

/**
 * 图片删除按钮样式
 *
 * 设计说明：
 * 位于图片右上角的删除按钮
 * 使用半透明黑色背景，白色图标，确保在各种图片上都能清晰显示
 */
.image-delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: rgba(0, 0, 0, 0.6);   /* 半透明黑色背景 */
  border-radius: 50%;                     /* 圆形按钮 */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/**
 * 添加图片按钮样式
 *
 * 设计说明：
 * 虚线边框的添加按钮，引导用户上传图片
 * 使用柔和的颜色和图标，不会过于突出
 */
.image-add-btn {
  width: 100%;
  aspect-ratio: 1;                        /* 保持1:1的宽高比 */
  border: 2rpx dashed #ddd;               /* 虚线边框 */
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  color: #999;
}

/**
 * 添加按钮文字样式 - 统一rpx单位版本
 */
.add-text {
  font-size: 24rpx; /* 24rpx: 参考profile页面小字体标准（辅助信息） */
  color: #999;
  margin-top: 8rpx;
}

/**
 * 上传状态提示样式
 *
 * 设计说明：
 * 显示图片上传进度的提示区域
 * 使用加载动画和文字提示，让用户了解上传状态
 */
.upload-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e5e5e5;
}

/**
 * 上传文字样式 - 统一rpx单位版本
 */
.upload-text {
  margin-left: 8rpx;
  font-size: 28rpx; /* 28rpx: 参考profile页面中字体标准（正文内容） */
  color: #666;
}

/**
 * 操作按钮区域样式 - 2025年美化版（一行三个按钮）
 *
 * 设计理念：
 * 操作按钮区域采用现代化的卡片设计，确保三个按钮在一行完美显示
 * 使用精致的渐变背景和阴影营造高端感，同时保证按钮的可用性
 *
 * 视觉特点：
 * - 一行布局：三个按钮始终在同一行显示
 * - 等宽分配：按钮平均分配容器宽度
 * - 卡片设计：独立的卡片容器
 * - 渐变背景：营造深度感
 * - 多层阴影：增加浮起效果
 * - 圆角设计：现代化视觉
 */
.action-area {
  /*
   * 间距设计 - 紧凑化
   * 32rpx: 减少与表单内容的间距，让页面更紧凑
   */
  margin-top: 32rpx;
  margin-bottom: 24rpx;

  /*
   * 布局设计 - 一行三个按钮
   * Flexbox布局，按钮水平排列，强制不换行
   */
  display: flex;
  gap: 12rpx; /* 按钮间距：12rpx，确保有足够空间但不会太宽 */
  flex-wrap: nowrap; /* 强制不换行，确保三个按钮在同一行 */
  justify-content: space-between; /* 平均分配空间 */
  align-items: stretch; /* 按钮高度一致 */

  /*
   * 内边距设计
   * 18rpx: 适中的内边距，为按钮留出足够空间
   */
  padding: 18rpx;

  /*
   * 宽度控制
   * 确保容器不会超出父元素
   */
  width: 100%;
  box-sizing: border-box; /* 包含内边距在总宽度内 */

  /*
   * 背景设计 - 渐变背景
   * 从白色到极浅的灰色，营造微妙的质感
   */
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);

  /*
   * 圆角设计
   * 16rpx: 与其他卡片保持一致
   */
  border-radius: 16rpx;

  /*
   * 多层阴影设计
   * 营造浮起的视觉效果
   */
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);

  /*
   * 边框设计
   * 极淡的边框增加精致感
   */
  border: 1rpx solid rgba(0, 0, 0, 0.02);
}

/**
 * 操作按钮样式 - 2025年美化版（一行三个按钮）
 *
 * 设计理念：
 * 按钮采用现代化的设计语言，确保三个按钮在一行完美显示
 * 通过精确的尺寸控制和渐变、阴影效果增加高端感
 * 不同类型的按钮使用不同的视觉处理
 */
.action-area .t-button {
  /*
   * 布局设计 - 等宽分配
   * 三个按钮平均分配容器宽度
   */
  flex: 1 1 0; /* 等宽分配，基础宽度为0，允许增长和收缩 */
  min-width: 0; /* 最小宽度为0，允许按钮收缩 */
  max-width: none; /* 不限制最大宽度 */

  /*
   * 尺寸设计
   * 80rpx: 适中的高度，既保证触摸体验又不会过高
   */
  height: 80rpx !important;

  /*
   * 字体设计 - 统一rpx单位
   * 28rpx: 参考profile页面中字体标准（正文内容）
   */
  font-size: 28rpx !important;

  /*
   * 字重设计
   * 600: 半粗体，既突出又不过于厚重
   */
  font-weight: 600 !important;

  /*
   * 内边距设计
   * 紧凑的内边距，确保按钮不会过宽
   */
  padding: 0 8rpx !important;

  /*
   * 圆角设计
   * 12rpx: 现代化的圆角
   */
  border-radius: 12rpx !important;

  /*
   * 文字控制
   * 确保文字在按钮中正确显示
   */
  white-space: nowrap !important; /* 文字不换行 */
  overflow: hidden !important; /* 隐藏溢出内容 */
  text-overflow: ellipsis !important; /* 长文本显示省略号 */
  text-align: center !important; /* 文字居中 */

  /*
   * 盒模型
   * 确保尺寸计算准确
   */
  box-sizing: border-box !important;

  /*
   * 过渡动画
   * 为悬停和点击效果提供平滑过渡
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

  /*
   * 阴影设计
   * 轻微的阴影增加立体感
   */
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important;
}

/**
 * 主要操作按钮（保存）特殊样式 - 渐变美化版
 * 使用醒目的蓝色渐变，在一行布局中脱颖而出
 */
.action-area .t-button--theme-primary {
  /*
   * 主要按钮渐变背景
   * 从亮蓝到深蓝的渐变，营造专业感和科技感
   */
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 50%, #1890ff 100%) !important;

  /*
   * 增强阴影
   * 蓝色阴影增加品牌感，让主要按钮更突出
   */
  box-shadow: 0 8rpx 32rpx rgba(79, 172, 254, 0.4) !important;

  /*
   * 文字颜色
   * 确保白色文字在蓝色背景上清晰可见
   */
  color: #ffffff !important;

  /*
   * 边框
   * 移除边框，让渐变背景更纯净
   */
  border: none !important;

  /*
   * 文字阴影
   * 轻微的文字阴影增加立体感
   */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1) !important;
}

/**
 * 次要操作按钮（保存为模板）特殊样式 - 绿色渐变
 * 使用温和的绿色渐变，表示保存操作但不如主按钮突出
 */
.action-area .t-button--theme-default:nth-child(2) {
  /*
   * 绿色渐变背景
   * 从浅绿到深绿的渐变，营造自然和安全感
   */
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 50%, #52c41a 100%) !important;

  /*
   * 绿色阴影
   * 与背景呼应的绿色阴影
   */
  box-shadow: 0 6rpx 24rpx rgba(82, 196, 26, 0.25) !important;

  /*
   * 文字颜色
   * 白色文字确保在绿色背景上清晰可见
   */
  color: #ffffff !important;

  /*
   * 边框
   * 移除边框，让渐变背景更纯净
   */
  border: none !important;

  /*
   * 文字阴影
   * 轻微的文字阴影增加立体感
   */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1) !important;
}

/**
 * 取消按钮特殊样式 - 橙色渐变
 * 使用温和的橙色渐变，表示取消操作
 */
.action-area .t-button--theme-default:last-child {
  /*
   * 橙色渐变背景
   * 从浅橙到深橙的渐变，营造温暖但警示的感觉
   */
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 50%, #ff7875 100%) !important;

  /*
   * 橙色阴影
   * 与背景呼应的橙色阴影
   */
  box-shadow: 0 6rpx 24rpx rgba(255, 120, 117, 0.25) !important;

  /*
   * 文字颜色
   * 白色文字确保在橙色背景上清晰可见
   */
  color: #ffffff !important;

  /*
   * 边框
   * 移除边框，让渐变背景更纯净
   */
  border: none !important;

  /*
   * 文字阴影
   * 轻微的文字阴影增加立体感
   */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1) !important;
}

/**
 * 按钮悬停效果 - 渐变美化版
 * 为每个按钮提供独特的悬停效果，与其渐变背景相匹配
 */
.action-area .t-button:hover {
  /*
   * 悬停时的变换
   * 轻微上移增加悬浮感
   */
  transform: translateY(-3rpx) scale(1.02) !important;

  /*
   * 过渡动画增强
   * 更流畅的悬停过渡效果
   */
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/**
 * 主要按钮（保存）悬停效果 - 蓝色渐变增强
 */
.action-area .t-button--theme-primary:hover {
  /*
   * 增强的蓝色渐变
   * 悬停时渐变更加鲜艳和动感
   */
  background: linear-gradient(135deg, #3fb5ff 0%, #00e5ff 50%, #0f7fff 100%) !important;

  /*
   * 增强的蓝色阴影
   * 更强烈的品牌色阴影，营造发光效果
   */
  box-shadow: 0 12rpx 48rpx rgba(63, 181, 255, 0.5) !important;
}

/**
 * 保存为模板按钮悬停效果 - 绿色渐变增强
 */
.action-area .t-button--theme-default:nth-child(2):hover {
  /*
   * 增强的绿色渐变
   * 悬停时绿色更加鲜艳
   */
  background: linear-gradient(135deg, #7de3df 0%, #fbb2d3 50%, #389e0d 100%) !important;

  /*
   * 增强的绿色阴影
   * 更强烈的绿色阴影，营造发光效果
   */
  box-shadow: 0 12rpx 48rpx rgba(56, 158, 13, 0.35) !important;
}

/**
 * 取消按钮悬停效果 - 橙色渐变增强
 */
.action-area .t-button--theme-default:last-child:hover {
  /*
   * 增强的橙色渐变
   * 悬停时橙色更加鲜艳
   */
  background: linear-gradient(135deg, #ffe0b2 0%, #ff9a8b 50%, #ff4d4f 100%) !important;

  /*
   * 增强的橙色阴影
   * 更强烈的橙色阴影，营造发光效果
   */
  box-shadow: 0 12rpx 48rpx rgba(255, 77, 79, 0.35) !important;
}

/**
 * 按钮按下效果 - 渐变美化版
 * 为所有按钮添加按下时的反馈效果
 */
.action-area .t-button:active {
  /*
   * 按下时的变换
   * 轻微下移和缩小，模拟按下效果
   */
  transform: translateY(1rpx) scale(0.98) !important;

  /*
   * 按下时的阴影
   * 减弱阴影，营造按下的感觉
   */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2) !important;

  /*
   * 过渡动画
   * 快速的按下反馈
   */
  transition: all 0.1s ease-out !important;
}

/**
 * 禁用状态样式 - 渐变美化版
 * 为禁用状态的按钮提供统一的视觉效果
 */
.action-area .t-button[disabled] {
  /*
   * 禁用时的背景
   * 灰色渐变表示不可用状态
   */
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%) !important;

  /*
   * 禁用时的文字颜色
   * 浅灰色文字
   */
  color: #bfbfbf !important;

  /*
   * 禁用时的阴影
   * 移除阴影，表示不可交互
   */
  box-shadow: none !important;

  /*
   * 禁用时的变换
   * 移除所有变换效果
   */
  transform: none !important;

  /*
   * 禁用时的文字阴影
   * 移除文字阴影
   */
  text-shadow: none !important;

  /*
   * 鼠标样式
   * 显示不可点击的鼠标样式
   */
  cursor: not-allowed !important;
}

/**
 * 选择器弹窗相关样式
 *
 * 功能说明：
 * 为选择器弹窗内的面板项提供样式
 * 主要用于时间选择器、讲师选择器等弹窗组件
 *
 * 设计考虑：
 * 在弹窗中，面板项之间需要更大的间距
 * 提升弹窗内容的可读性和操作体验
 */
.panel-item {
  margin-bottom: 32rpx;              /* 下边距：面板项之间的间隔 */
}

/**
 * 页面标题区域样式 - 高端美化版
 *
 * 设计理念：
 * 页面标题采用现代化的头部设计，通过渐变背景和精致的排版营造专业感
 * 使用卡片式设计与表单区域形成统一的视觉语言
 *
 * 视觉特点：
 * - 渐变背景：营造深度和层次感
 * - 多层阴影：增加浮起的视觉效果
 * - 圆角设计：现代化的视觉效果
 * - 精致边框：增加细节质感
 */
.page-header {
  /*
   * 布局设计
   * Flexbox布局，支持标题和操作的灵活排列
   */
  display: flex;
  justify-content: space-between;
  align-items: center;

  /*
   * 间距设计
   * 32rpx: 与下方内容保持更大的间距
   */
  margin-bottom: 32rpx;
  
  

  /*
   * 内边距设计
   * 24rpx: 为内容提供舒适的内边距
   */
  padding: 24rpx;
  box-sizing: border-box;

  /*
   * 背景设计 - 渐变背景
   * 从白色到极浅的蓝色，营造清新的感觉
   */
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e3f2fd 100%);

  /*
   * 圆角设计
   * 16rpx: 与表单分区保持一致的圆角
   */
  border-radius: 16rpx;

  /*
   * 多层阴影设计
   * 营造浮起的视觉效果
   */
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.08),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);

  /*
   * 边框设计
   * 极淡的边框增加精致感
   */
  border: 1rpx solid rgba(0, 0, 0, 0.02);
}

/**
 * 页面标题文字样式 - 统一rpx单位版本
 *
 * 设计理念：
 * 标题文字采用现代化的排版设计，参考profile页面规范使用32rpx大字体
 */
.page-title {
  /*
   * 字体设计 - 统一rpx单位
   * 32rpx: 参考profile页面大字体标准（主要标题）
   */
  font-size: 32rpx;

  /*
   * 字重设计
   * 700: 粗体，突出标题
   */
  font-weight: 700;

  /*
   * 颜色设计 - 渐变文字色
   * 从深蓝到深灰的渐变，营造专业感
   */
  background: linear-gradient(135deg, #1e3a8a 0%, #374151 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  /*
   * 布局设计
   * 占据剩余空间
   */
  flex: 1;

  /*
   * 字母间距
   * 微妙的字母间距增加精致感
   */
  letter-spacing: 1rpx;

  /*
   * 文字阴影
   * 极淡的阴影增加立体感
   */
  text-shadow: 0 1rpx 2rpx rgba(30, 58, 138, 0.1);
}



/**
 * 模板选择按钮样式 - 高端美化版
 *
 * 设计理念：
 * 模板选择按钮采用精致的设计，通过渐变和阴影增加高端感
 */
.template-btn {
  /*
   * 尺寸设计
   * 更大的尺寸提升触摸体验
   */
  min-width: 140rpx !important;
  height: 72rpx !important;

  /*
   * 字体设计
   * 28rpx: 清晰可读的字体大小
   */
  font-size: 28rpx !important;
  font-weight: 500 !important;

  /*
   * 圆角设计
   * 12rpx: 现代化的圆角
   */
  border-radius: 12rpx !important;

  /*
   * 渐变背景
   * 蓝色渐变营造专业感
   */
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%) !important;

  /*
   * 阴影设计
   * 蓝色阴影增加品牌感
   */
  box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.2) !important;

  /*
   * 过渡动画
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/**
 * 模板选择按钮悬停效果
 */
.template-btn:hover {
  transform: translateY(-2rpx) !important;
  box-shadow: 0 6rpx 24rpx rgba(24, 144, 255, 0.3) !important;
}

/**
 * 页面标题区域内的模板选择按钮样式
 *
 * 设计理念：
 * 位于页面标题右侧的模板选择按钮，采用紧凑的设计
 * 宽度只比文字稍宽，与标题区域协调统一
 */
.template-btn-header {
  /*
   * 尺寸设计
   * 紧凑的尺寸，宽度只比文字稍宽
   */
  padding: 6rpx 12rpx !important; /* 内边距：上下6rpx，左右12rpx，让按钮紧凑包裹文字 */
  height: 56rpx !important; /* 高度：56rpx，更紧凑的高度 */
  min-width: auto !important; /* 最小宽度：自动，让按钮根据内容调整宽度 */
  width: auto !important; /* 宽度：自动，完全根据内容调整 */

  /*
   * 字体设计 - 统一rpx单位
   * 紧凑的字体，与按钮尺寸协调
   */
  font-size: 24rpx !important; /* 24rpx: 参考profile页面小字体标准，适合紧凑按钮 */
  font-weight: 500 !important;
  line-height: 1.2 !important; /* 行高：紧凑的行高，减少垂直空间 */

  /*
   * 圆角设计
   * 与标题区域的圆角保持一致
   */
  border-radius: 12rpx !important;

  /*
   * 渐变背景
   * 与原按钮保持相同的蓝色渐变
   */
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%) !important;

  /*
   * 阴影设计
   * 稍微减弱的阴影，与标题区域协调
   */
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.15) !important;

  /*
   * 过渡动画
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

  /*
   * 文字和布局控制
   * 确保按钮紧凑显示
   */
  white-space: nowrap !important; /* 文字不换行 */
  display: inline-flex !important; /* 内联弹性布局，紧凑包裹内容 */
  align-items: center !important; /* 垂直居中对齐 */
  justify-content: center !important; /* 水平居中对齐 */
  flex-shrink: 0 !important; /* 不允许收缩，保持最小尺寸 */
}

/**
 * 页面标题区域内的模板选择按钮悬停效果
 */
.template-btn-header:hover {
  transform: translateY(-1rpx) !important; /* 轻微上移，比原来的-2rpx更subtle */
  box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.25) !important;
}

/**
 * 快速按钮组样式 - 2025年版本
 * 确保三个时间快速设置按钮始终在同一行显示
 */
.quick-buttons {
  /*
   * 布局设计
   * Flexbox布局，按钮水平排列，平均分配空间
   */
  display: flex;
  gap: 8rpx; /* 合理的间距，确保视觉效果 */

  /*
   * 间距设计
   */
  margin-top: 12rpx;

  /*
   * 对齐方式
   * 平均分配空间，让三个按钮等宽显示
   */
  justify-content: space-between;
  align-items: stretch;

  /*
   * 换行控制
   * 强制不换行，三个按钮必须在同一行
   */
  flex-wrap: nowrap;

  /*
   * 宽度控制
   * 确保容器占满父元素宽度
   */
  width: 100%;
  box-sizing: border-box;
}

/**
 * 快速选择按钮样式 - 2025年版本
 *
 * 功能说明：
 * 时长快速选择按钮的样式，确保三个按钮（1h、1.5h、2h）在同一行完美显示
 *
 * 设计特点：
 * - 等宽分配：三个按钮平均分配容器宽度
 * - 合适高度：56rpx高度，适合触摸操作
 * - 清晰字号：26rpx字号，清晰可读
 * - 适度内边距：提供舒适的内容空间
 * - 圆角设计：28rpx圆角，现代化视觉效果
 * - 文字居中：确保文字在按钮中居中显示
 */
.quick-btn {
  /*
   * 尺寸设计 - 等宽分配，确保三个按钮在同一行
   */
  flex: 1 !important; /* 平均分配空间 */
 

  /*
   * 字体设计 - 统一rpx单位
   * 24rpx: 参考profile页面小字体标准，适合快速按钮
   */
  font-size: 24rpx !important;
  font-weight: 500 !important;

  /*
   * 内边距设计
   */
  padding: 0 12rpx !important;

  /*
   * 圆角设计
   */
  border-radius: 28rpx !important;

  /*
   * 文字设置
   */
  white-space: nowrap !important;
  text-align: center !important; /* 文字居中 */

  /*
   * 背景设计
   */
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;

  /*
   * 边框设计
   */
  border: 1rpx solid rgba(0, 0, 0, 0.08) !important;

  /*
   * 文字颜色
   */
  color: #374151 !important;

  /*
   * 阴影设计
   */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06) !important;

  /*
   * 过渡动画
   */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;

  /*
   * 盒模型
   * 确保内边距和边框包含在总宽度内
   */
  box-sizing: border-box !important;
}

/**
 * 快速按钮悬停效果 - 高端美化版
 * 增加丰富的交互反馈，提升用户体验
 */
.quick-btn:hover {
  /*
   * 悬停时的背景变化
   * 更明显的白色背景
   */
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%) !important;

  /*
   * 悬停时的边框变化
   * 稍深的边框色
   */
  border-color: rgba(0, 0, 0, 0.12) !important;

  /*
   * 悬停时的阴影增强
   * 更明显的阴影营造悬浮效果
   */
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important;

  /*
   * 轻微的上移效果
   * 增强悬停的视觉反馈
   */
  transform: translateY(-1rpx) !important;
}

/**
 * 操作按钮统一样式（增强版本）- 修复右侧出界问题
 *
 * 功能说明：
 * 为操作区域的按钮提供统一的增强样式
 * 使用灵活的尺寸，提升触摸体验同时防止出界
 *
 * 设计理念：
 * - 灵活尺寸：使用flex布局自适应宽度，防止出界
 * - 统一风格：操作按钮保持一致的视觉效果
 * - 响应式设计：在不同屏幕尺寸下都能正常显示
 * - 盒模型：使用border-box确保尺寸计算准确
 */
.action-area .t-button {
  min-width: 160rpx !important;       /* 最小宽度：160rpx，确保基本的触摸区域 */
  max-width: none !important;        /* 移除最大宽度限制，允许自适应 */
  height: 80rpx !important;          /* 高度：80rpx，约40px */
  font-size: 28rpx !important;       /* 字体大小：28rpx，适中大小 */
  border-radius: 16rpx !important;   /* 圆角：16rpx，约8px */
  padding: 0 20rpx !important;       /* 内边距：左右20rpx，更紧凑 */
  box-sizing: border-box !important; /* 盒模型：边框盒模型 */
  flex: 1 1 auto !important;         /* 弹性布局：允许增长和收缩 */
  white-space: nowrap !important;    /* 文字不换行 */
  overflow: hidden !important;       /* 防止内容溢出 */
  text-overflow: ellipsis !important; /* 长文本显示省略号 */
}

/**
 * 响应式设计 - 小屏幕适配
 *
 * 设计说明：
 * 针对小屏幕设备（宽度小于750rpx）的样式优化
 * 主要调整字号、间距、按钮大小等，确保在小屏幕上的可用性
 *
 * 适配策略：
 * 1. 减小内边距：节省屏幕空间
 * 2. 调整字号：保持可读性的同时节省空间
 * 3. 优化按钮：调整按钮大小，确保触摸体验
 * 4. 保持比例：各元素之间的比例关系保持一致
 *
 * 断点选择：
 * 750rpx约等于375px，对应iPhone 6/7/8等设备的宽度
 * 这是移动端开发中常用的断点
 */
@media (max-width: 750rpx) {
  /**
   * 表单区域小屏幕适配
   * 减小内边距，为内容留出更多空间
   */
  .form-area {
    padding: 8rpx;                   /* 内边距：从16rpx减少到8rpx */
  }

  /**
   * 表单项小屏幕适配
   * 调整内边距和间距，优化空间利用
   */
  .form-item {
    padding: 10rpx 12rpx;            /* 内边距：减小到10rpx 12rpx */
    margin-bottom: 10rpx;            /* 下边距：从16rpx减少到10rpx */
  }

  /**
   * 标签文字小屏幕适配
   * 适当减小字号，保持可读性
   */
  .label {
    font-size: 28rpx;                /* 字体大小：从32rpx减少到28rpx */
  }

  /**
   * 输入提示文字小屏幕适配
   * 减小字号，节省空间
   */
  .input-tip {
    font-size: 20rpx;                /* 字体大小：从24rpx减少到20rpx */
  }

  /**
   * 错误提示文字小屏幕适配
   * 保持与输入提示相同的字号
   */
  .error-text {
    font-size: 20rpx;                /* 字体大小：从24rpx减少到20rpx */
  }

  /**
   * 操作按钮小屏幕适配
   * 调整按钮大小，确保在小屏幕上的可用性
   */
  .action-area .t-button {
    font-size: 22rpx;                /* 字体大小：减小字号 */
    height: 40rpx;                   /* 高度：减小高度 */
  }

  /**
   * 分区标题小屏幕适配
   * 适当减小字号，保持层次感
   */
  .form-section-title {
    font-size: 34rpx;                /* 字体大小：从36rpx减少到34rpx */
  }

  /**
   * 页面标题小屏幕适配
   * 显著减小字号，节省顶部空间
   */
  .page-title {
    font-size: 24rpx;                /* 字体大小：从32rpx减少到24rpx */
  }

  /**
   * 快速按钮小屏幕适配
   * 在小屏幕上稍微调整尺寸，但保持在同一行
   */
  .quick-btn {
    height: 48rpx !important;        /* 高度：稍微减小到48rpx */
    font-size: 24rpx !important;     /* 字体大小：稍微减小到24rpx */
    padding: 0 8rpx !important;      /* 内边距：稍微减小到8rpx */
    border-radius: 24rpx !important; /* 圆角：调整为高度的一半 */
  }

  /**
   * 图片网格小屏幕适配
   * 调整网格间距，优化空间利用
   */
  .image-grid {
    gap: 12rpx;                      /* 网格间距：从16rpx减少到12rpx */
  }

  /**
   * 标签描述文字小屏幕适配
   */
  .label-desc {
    font-size: 22rpx;                /* 字体大小：从24rpx减少到22rpx */
  }

  /**
   * 添加按钮文字小屏幕适配
   */
  .add-text {
    font-size: 22rpx;                /* 字体大小：从24rpx减少到22rpx */
  }
}

/**
 * 响应式设计 - 小屏幕适配（一行三个按钮）
 *
 * 功能说明：
 * 为小屏幕设备优化按钮样式，确保三个按钮仍然在一行显示
 * 通过减小字体、内边距和间距来适应小屏幕
 *
 * 设计考虑：
 * 在小屏幕上，保持一行三个按钮的布局，但调整尺寸以适应屏幕
 * 确保用户体验一致，同时防止出界
 */
@media (max-width: 750rpx) {
  .action-area .t-button {
    min-width: 0 !important;         /* 最小宽度：移除限制，允许完全自适应 */
    max-width: none !important;      /* 最大宽度：移除限制 */
    height: 68rpx !important;        /* 高度：稍微减小为68rpx */
    font-size: 24rpx !important;     /* 字体大小：减小为24rpx */
    border-radius: 10rpx !important; /* 圆角：稍微减小为10rpx */
    padding: 0 6rpx !important;      /* 内边距：减小为6rpx，更紧凑 */
  }

  /**
   * 操作区域小屏幕适配
   * 减小内边距和间距，为按钮留出更多空间
   */
  .action-area {
    padding: 14rpx !important;       /* 内边距：减小为14rpx */
    gap: 8rpx !important;            /* 间距：减小为8rpx，让按钮更紧凑 */
  }
}

/**
 * 响应式设计 - 极小屏幕适配（宽度小于320px的设备）
 *
 * 功能说明：
 * 针对极小屏幕设备（如iPhone SE等）的特殊适配
 * 在极小屏幕上改为垂直布局，确保按钮可用性
 *
 * 设计策略：
 * - 垂直布局：三个按钮垂直排列
 * - 最小化内边距和间距
 * - 保持按钮的可读性和可点击性
 */
@media (max-width: 640rpx) {
  /**
   * 极小屏幕下的操作区域适配
   * 改为垂直布局，确保按钮完全可见
   */
  .action-area {
    padding: 12rpx !important;       /* 内边距：进一步减小为12rpx */
    gap: 10rpx !important;           /* 间距：垂直间距10rpx */
    flex-direction: column !important; /* 垂直排列，防止水平出界 */
    align-items: stretch !important; /* 按钮拉伸填满宽度 */
  }

  /**
   * 极小屏幕下的操作按钮适配
   * 每个按钮占满容器宽度
   */
  .action-area .t-button {
    min-width: 0 !important;         /* 最小宽度：移除限制 */
    width: 100% !important;          /* 宽度：占满容器宽度 */
    flex: none !important;           /* 移除弹性布局，使用固定宽度 */
    height: 64rpx !important;        /* 高度：适中的触摸高度 */
    font-size: 28rpx !important;     /* 字体大小：28rpx，参考profile页面中字体标准 */
    padding: 0 16rpx !important;     /* 内边距：适中的内边距 */
    border-radius: 12rpx !important; /* 圆角：保持现代化外观 */
  }

  /**
   * 极小屏幕下的快速按钮适配
   * 保持合理的尺寸，确保用户体验
   */
  .quick-btn {
    height: 44rpx !important;        /* 高度：保持合理的触摸区域 */
    font-size: 24rpx !important;     /* 字体大小：24rpx，参考profile页面小字体标准 */
    padding: 0 6rpx !important;      /* 内边距：适度减小 */
    border-radius: 22rpx !important; /* 圆角：调整为高度的一半 */
  }
}

/**
 * 文件总结：course-edit.wxss
 *
 * 这个样式文件实现了一个完整的课程编辑页面设计，主要特点：
 *
 * 1. 表单设计系统：
 *    - 卡片式布局：每个表单项独立的白色卡片
 *    - 统一的视觉语言：字号、颜色、间距的系统化设计
 *    - 清晰的信息层次：标签、输入、提示、错误的层次分明
 *    - 必填字段标识：红色星号自动标识必填字段
 *
 * 2. 用户体验优化：
 *    - 错误提示：红色文字明确显示验证错误
 *    - 快速操作：时长快选按钮提升输入效率
 *    - 操作反馈：按钮状态和加载提示
 *    - 触摸友好：按钮大小适合移动端操作
 *
 * 3. 响应式设计：
 *    - 断点设计：750rpx断点适配小屏幕设备
 *    - 比例保持：各元素比例关系在不同屏幕下保持一致
 *    - 空间优化：小屏幕下合理调整间距和字号
 *    - 一致性：不同屏幕下保持相同的用户体验
 *
 * 4. 技术特点：
 *    - Flexbox布局：现代化的布局方式
 *    - CSS变量：通过统一的设计token保持一致性
 *    - 媒体查询：响应式设计的标准实现
 *    - 优先级控制：合理使用!important确保样式生效
 *
 * 5. 设计原则：
 *    - 移动优先：针对移动端优化的设计
 *    - 内容为王：突出表单内容，减少视觉干扰
 *    - 操作便捷：简化用户操作流程
 *    - 反馈及时：即时的视觉反馈和状态提示
 *
 * 与您熟悉的技术对比：
 * - 响应式设计：类似于Bootstrap的响应式网格系统
 * - 组件样式：类似于WPF的样式和模板系统
 * - 媒体查询：类似于CSS3的响应式设计标准
 * - 布局系统：类似于现代前端框架的布局组件
 */